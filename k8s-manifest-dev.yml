apiVersion: v1
kind: ServiceAccount
metadata:
  name: payment-service-ai-sa
  namespace: ruh-common-dev
  labels:
    name: payment-service-ai-sa
    namespace: ruh-common-dev
    app: payment-service-ai
    deployment: payment-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-service-ai-dp
  namespace: ruh-common-dev
  labels:
    name: payment-service-ai-dp
    namespace: ruh-common-dev
    app: payment-service-ai
    serviceaccount: payment-service-ai-sa
    deployment: payment-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: payment-service-ai
      deployment: payment-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-common-dev
        app: payment-service-ai
        deployment: payment-service-ai-dp
    spec:
      serviceAccountName: payment-service-ai-sa
      containers:
        - name: payment-service-ai
          image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
          resources:
            requests:
              memory: 64Mi
              cpu: 50m
            limits:
              memory: 1024Mi
              cpu: 250m
          ports:
            - containerPort: 8000
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:
      #   eks.amazonaws.com/capacityType: SPOT
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: payment-service-ai-svc
  namespace: ruh-common-dev
spec:
  selector:
    app: payment-service-ai
    deployment: payment-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:payment-service-node-executor-hpa
#   namespace:ruh-common-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:payment-service-node-executor-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: payment-service-ingress
#   namespace: ruh-common-dev
# spec:
#   ingressClassName: nginx
#   rules:
#     - host: payment-service-dev.rapidinnovation.dev
#       http:
#         paths:
#           - path: /
#             pathType: Prefix
#             backend:
#               service:
#                 name: payment-service-ai-svc
#                 port:
#                   number: 80
