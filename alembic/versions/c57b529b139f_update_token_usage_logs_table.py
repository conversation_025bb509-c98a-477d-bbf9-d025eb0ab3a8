"""Update token_usage_logs table

Revision ID: c57b529b139f
Revises: 
Create Date: 2025-06-25 15:37:29.600655

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c57b529b139f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('payment_customers',
    sa.Column('organisation_id', sa.String(), nullable=False),
    sa.Column('stripe_customer_id', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.PrimaryKeyConstraint('organisation_id')
    )
    op.create_index(op.f('ix_payment_customers_organisation_id'), 'payment_customers', ['organisation_id'], unique=False)
    op.create_index(op.f('ix_payment_customers_stripe_customer_id'), 'payment_customers', ['stripe_customer_id'], unique=True)
    op.create_table('payment_plans',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('plan_id_code', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('credit_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('stripe_price_id', sa.String(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_payment_plans_id'), 'payment_plans', ['id'], unique=False)
    op.create_index(op.f('ix_payment_plans_plan_id_code'), 'payment_plans', ['plan_id_code'], unique=True)
    op.create_index(op.f('ix_payment_plans_stripe_price_id'), 'payment_plans', ['stripe_price_id'], unique=False)
    op.create_table('payment_webhook_events',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('event_type', sa.String(), nullable=False),
    sa.Column('processed', sa.Boolean(), nullable=True),
    sa.Column('processing_attempts', sa.Integer(), nullable=True),
    sa.Column('event_data', sa.Text(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('credit_deductions',
    sa.Column('id', sa.UUID(), server_default=sa.text('(gen_random_uuid())'), nullable=False),
    sa.Column('organisation_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('agent_id', sa.String(), nullable=True),
    sa.Column('credits_deducted', sa.Numeric(precision=10, scale=4), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['organisation_id'], ['payment_customers.organisation_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_credit_deductions_agent_id'), 'credit_deductions', ['agent_id'], unique=False)
    op.create_index(op.f('ix_credit_deductions_organisation_id'), 'credit_deductions', ['organisation_id'], unique=False)
    op.create_index(op.f('ix_credit_deductions_user_id'), 'credit_deductions', ['user_id'], unique=False)
    op.create_table('payment_subscriptions',
    sa.Column('id', sa.UUID(), server_default=sa.text('(gen_random_uuid())'), nullable=False),
    sa.Column('organisation_id', sa.String(), nullable=False),
    sa.Column('plan_id', sa.Integer(), nullable=False),
    sa.Column('stripe_subscription_id', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'CANCELED', 'INCOMPLETE', 'PAST_DUE', 'TRIALING', name='paymentsubscriptionstatus'), nullable=False),
    sa.Column('current_period_start', sa.DateTime(timezone=True), nullable=True),
    sa.Column('current_period_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['organisation_id'], ['payment_customers.organisation_id'], ),
    sa.ForeignKeyConstraint(['plan_id'], ['payment_plans.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_payment_subscriptions_organisation_id'), 'payment_subscriptions', ['organisation_id'], unique=False)
    op.create_index(op.f('ix_payment_subscriptions_plan_id'), 'payment_subscriptions', ['plan_id'], unique=False)
    op.create_index(op.f('ix_payment_subscriptions_stripe_subscription_id'), 'payment_subscriptions', ['stripe_subscription_id'], unique=True)
    op.create_table('token_usage_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('organisation_id', sa.String(), nullable=False),
    sa.Column('input_tokens', sa.Integer(), nullable=False),
    sa.Column('output_tokens', sa.Integer(), nullable=False),
    sa.Column('input_cost', sa.Numeric(precision=10, scale=6), nullable=False),
    sa.Column('output_cost', sa.Numeric(precision=10, scale=6), nullable=False),
    sa.Column('total_cost', sa.Numeric(precision=10, scale=6), nullable=False),
    sa.Column('total_credits', sa.Numeric(precision=10, scale=4), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.ForeignKeyConstraint(['organisation_id'], ['payment_customers.organisation_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'organisation_id', 'date', name='_user_org_date_uc')
    )
    op.create_index(op.f('ix_token_usage_logs_date'), 'token_usage_logs', ['date'], unique=False)
    op.create_index(op.f('ix_token_usage_logs_id'), 'token_usage_logs', ['id'], unique=False)
    op.create_index(op.f('ix_token_usage_logs_organisation_id'), 'token_usage_logs', ['organisation_id'], unique=False)
    op.create_index(op.f('ix_token_usage_logs_user_id'), 'token_usage_logs', ['user_id'], unique=False)
    op.create_table('payment_transactions',
    sa.Column('id', sa.UUID(), server_default=sa.text('(gen_random_uuid())'), nullable=False),
    sa.Column('organisation_id', sa.String(), nullable=False),
    sa.Column('transaction_type', sa.Enum('PLAN_PURCHASE', 'CREDIT_PURCHASE', 'REFUND', name='paymenttransactiontype'), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'COMPLETED', 'FAILED', name='paymenttransactionstatus'), nullable=False),
    sa.Column('amount_currency', sa.Integer(), nullable=False),
    sa.Column('currency', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('stripe_charge_id', sa.String(), nullable=True),
    sa.Column('stripe_invoice_id', sa.String(), nullable=True),
    sa.Column('subscription_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['organisation_id'], ['payment_customers.organisation_id'], ),
    sa.ForeignKeyConstraint(['subscription_id'], ['payment_subscriptions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_payment_transactions_organisation_id'), 'payment_transactions', ['organisation_id'], unique=False)
    op.create_index(op.f('ix_payment_transactions_stripe_charge_id'), 'payment_transactions', ['stripe_charge_id'], unique=False)
    op.create_index(op.f('ix_payment_transactions_stripe_invoice_id'), 'payment_transactions', ['stripe_invoice_id'], unique=False)
    op.create_index(op.f('ix_payment_transactions_transaction_type'), 'payment_transactions', ['transaction_type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_payment_transactions_transaction_type'), table_name='payment_transactions')
    op.drop_index(op.f('ix_payment_transactions_stripe_invoice_id'), table_name='payment_transactions')
    op.drop_index(op.f('ix_payment_transactions_stripe_charge_id'), table_name='payment_transactions')
    op.drop_index(op.f('ix_payment_transactions_organisation_id'), table_name='payment_transactions')
    op.drop_table('payment_transactions')
    op.drop_index(op.f('ix_token_usage_logs_user_id'), table_name='token_usage_logs')
    op.drop_index(op.f('ix_token_usage_logs_organisation_id'), table_name='token_usage_logs')
    op.drop_index(op.f('ix_token_usage_logs_id'), table_name='token_usage_logs')
    op.drop_index(op.f('ix_token_usage_logs_date'), table_name='token_usage_logs')
    op.drop_table('token_usage_logs')
    op.drop_index(op.f('ix_payment_subscriptions_stripe_subscription_id'), table_name='payment_subscriptions')
    op.drop_index(op.f('ix_payment_subscriptions_plan_id'), table_name='payment_subscriptions')
    op.drop_index(op.f('ix_payment_subscriptions_organisation_id'), table_name='payment_subscriptions')
    op.drop_table('payment_subscriptions')
    op.drop_index(op.f('ix_credit_deductions_user_id'), table_name='credit_deductions')
    op.drop_index(op.f('ix_credit_deductions_organisation_id'), table_name='credit_deductions')
    op.drop_index(op.f('ix_credit_deductions_agent_id'), table_name='credit_deductions')
    op.drop_table('credit_deductions')
    op.drop_table('payment_webhook_events')
    op.drop_index(op.f('ix_payment_plans_stripe_price_id'), table_name='payment_plans')
    op.drop_index(op.f('ix_payment_plans_plan_id_code'), table_name='payment_plans')
    op.drop_index(op.f('ix_payment_plans_id'), table_name='payment_plans')
    op.drop_table('payment_plans')
    op.drop_index(op.f('ix_payment_customers_stripe_customer_id'), table_name='payment_customers')
    op.drop_index(op.f('ix_payment_customers_organisation_id'), table_name='payment_customers')
    op.drop_table('payment_customers')
    # ### end Alembic commands ###
