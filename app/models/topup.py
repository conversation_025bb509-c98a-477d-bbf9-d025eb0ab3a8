"""
Database models for the Topup Plans.
"""

import enum
from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Boolean,
    Numeric,
    Text,
    ForeignKey,
    Date,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.sql import func
from sqlalchemy.types import Enum as SQLEnum

from app.db.base_class import Base


class TopupPlan(Base):
    __tablename__ = "topup_plans"

    id = Column(Integer, primary_key=True, index=True)
    plan_id_code = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=False)
    credit_amount = Column(Numeric(10, 2), nullable=False)
    price = Column(Numeric(10, 2), nullable=False, default=0.00)
    stripe_price_id = Column(String, nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<TopupPlan(plan_id_code='{self.plan_id_code}', name='{self.name}')>"