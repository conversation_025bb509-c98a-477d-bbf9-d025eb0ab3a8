"""Add credits fields to subscription and deduction tables

Revision ID: 6f0638205bcf
Revises: c57b529b139f
Create Date: 2025-06-25 16:53:00.915255

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6f0638205bcf'
down_revision = 'c57b529b139f'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('credit_deductions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('remaining_credits', sa.Numeric(precision=20, scale=10), nullable=False, server_default='0.0'))

    with op.batch_alter_table('payment_subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_credits', sa.Numeric(precision=20, scale=10), nullable=False, server_default='0.0'))
        batch_op.add_column(sa.Column('current_credits', sa.Numeric(precision=20, scale=10), nullable=False, server_default='0.0'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('payment_subscriptions', schema=None) as batch_op:
        batch_op.drop_column('current_credits')
        batch_op.drop_column('subscription_credits')

    with op.batch_alter_table('credit_deductions', schema=None) as batch_op:
        batch_op.drop_column('remaining_credits')
    # ### end Alembic commands ###
