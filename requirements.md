# Requirements for Payment Microservice

This document outlines the requirements for the payment microservice, updated to reflect the credit-centric billing model and gRPC architecture.

## System Architecture Context

The system follows a microservices architecture with gRPC communication:
1.  The **API Gateway** authenticates users and routes payment-related requests to the `payment-service`, passing the `user_id`.
2.  The **User Service** is the source of truth for all user entitlements (plan name, credit balance, model access, log retention, support tier).
3.  The **Payment Service** orchestrates transactions with <PERSON><PERSON> and communicates with other services via gRPC.
4.  **gRPC Communication**: The payment service exposes gRPC endpoints for internal service communication and uses gRPC clients to communicate with the user-service.
5.  **Webhook Processing**: Stripe webhooks are processed via HTTP endpoints and trigger gRPC calls to update user entitlements.

## Functional Requirements

### Core Payment Processing
- **FR1: gRPC Service Interface:** The service must expose gRPC endpoints for payment operations including subscription creation, customer portal access, and payment status queries.
- **FR2: Subscription Creation:** The service must create Stripe Checkout sessions to allow users to subscribe to the "Standard" and "Pro" plans via gRPC calls.
- **FR3: Secure Payment Processing:** All payments must be processed securely via Stripe with proper webhook signature verification.
- **FR4: Customer Management:** The service must manage Stripe customer creation, retrieval, and mapping to internal user IDs.

### Entitlement Management
- **FR5: Entitlement Provisioning:** Upon a successful new subscription, the service must trigger gRPC calls to the `user-service` to set the user's specific entitlements based on their chosen plan.
    - **Standard Plan:**
        - Set monthly credits to 5,000.
        - Set LLM model access to "Standard".
        - Set traceability log retention to "3 Months".
        - Set technical support tier to "Email".
    - **Pro Plan:**
        - Set monthly credits to 20,000.
        - Set LLM model access to "Premium & Custom".
        - Set traceability log retention to "1 Year".
        - Set technical support tier to "24/7 Dedicated".
- **FR6: Subscription Renewals:** On each successful monthly renewal, the service must use gRPC to instruct the `user-service` to reset the user's credit balance to their plan's limit (5,000 or 20,000).
- **FR7: Subscription Management:** Users must be able to access the Stripe Customer Portal to manage their subscription via gRPC endpoints.
- **FR8: Subscription Cancellation & Downgrading:** Upon cancellation, the service must listen for webhooks and use gRPC to instruct the `user-service` to downgrade the user's entitlements to the "Free" plan state.
    - **Free Plan State:**
        - Set monthly credits to 500.
        - Set LLM model access to "Base".
        - Set traceability log retention to "None".
        - Set technical support tier to "Community".
- **FR9: Credit Top-up:** Users on any plan can purchase additional credits. The service will process the one-time payment and use gRPC to instruct the `user-service` to add the purchased credits to the user's current balance.

### Service Communication
- **FR10: gRPC Client Integration:** The service must implement gRPC clients to communicate with the `user-service` for entitlement updates.
- **FR11: Transaction Logging:** All payment transactions must be logged with proper audit trails and transaction IDs.
- **FR12: Error Handling:** The service must implement robust error handling for both Stripe API failures and gRPC communication failures.

## Non-Functional Requirements

- **NFR1: Security:**
    - Stripe API keys and webhook secrets must be stored securely as environment variables.
    - All incoming Stripe webhooks must be cryptographically verified.
    - gRPC communication must be secured with proper authentication and authorization.
    - Database connections must use connection pooling and proper credential management.
- **NFR2: Reliability:**
    - The service must gracefully handle failures in gRPC communication with other services (e.g., using retries and circuit breakers).
    - Webhook processing must be idempotent to prevent duplicate provisioning.
    - The service must implement proper health checks for both HTTP and gRPC endpoints.
- **NFR3: Performance:**
    - gRPC services must handle concurrent requests efficiently with proper thread pool management.
    - Database operations must be optimized with proper indexing and connection pooling.
    - Webhook processing must be asynchronous to handle high-volume events.
- **NFR4: Configuration:** All external configuration (Stripe keys, price IDs, database URLs, service endpoints) must be managed via environment variables with proper validation.
- **NFR5: Observability:** The service must provide structured logging for key events, including gRPC requests, Stripe API calls, webhook processing, and database operations.
- **NFR6: Scalability:** The service must be designed to scale horizontally with stateless operation and proper resource management.

## Technical Stack

- **Language:** Python 3.11+
- **Package Manager:** Poetry
- **gRPC Framework:** grpcio, grpcio-tools
- **Web Framework:** FastAPI (for webhooks and health checks)
- **Database:** PostgreSQL with SQLAlchemy ORM
- **Server:** Uvicorn (for HTTP), gRPC server (for gRPC)
- **Primary Dependencies:**
  - `grpcio`, `grpcio-tools` (gRPC communication)
  - `stripe` (Stripe API integration)
  - `sqlalchemy`, `alembic` (Database ORM and migrations)
  - `psycopg2-binary` (PostgreSQL driver)
  - `pydantic`, `pydantic-settings` (Data validation and settings)
  - `structlog` (Structured logging)
  - `python-dotenv` (Environment configuration)