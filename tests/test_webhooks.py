"""
Tests for webhook handlers.
"""

import pytest
import json
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from main import app
from app.models.stripe_customer import Base, get_db, StripeCustomer

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_webhooks.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


class TestWebhookHandlers:
    """Test cases for webhook handlers."""
    
    @patch('stripe.Webhook.construct_event')
    @patch('app.services.user_service_client.user_service_client.update_user_entitlements')
    @patch('stripe.Subscription.retrieve')
    def test_checkout_session_completed_subscription(
        self, 
        mock_subscription_retrieve,
        mock_update_entitlements,
        mock_construct_event
    ):
        """Test handling checkout.session.completed for subscription."""
        # Mock webhook event
        mock_event = {
            'type': 'checkout.session.completed',
            'id': 'evt_test_123',
            'data': {
                'object': {
                    'id': 'cs_test_123',
                    'mode': 'subscription',
                    'customer': 'cus_test_123',
                    'subscription': 'sub_test_123',
                    'metadata': {'user_id': 'user_123'}
                }
            }
        }
        mock_construct_event.return_value = mock_event
        
        # Mock Stripe subscription
        mock_subscription_retrieve.return_value = {
            'items': {
                'data': [{'price': {'id': 'price_standard'}}]
            }
        }
        
        # Mock user service response
        mock_update_entitlements.return_value = True
        
        # Send webhook
        response = client.post(
            "/stripe-webhook",
            data=json.dumps(mock_event),
            headers={
                "stripe-signature": "test_signature",
                "content-type": "application/json"
            }
        )
        
        assert response.status_code == 200
        assert response.json() == {"status": "success"}
        
        # Verify customer was stored
        db = TestingSessionLocal()
        customer = db.query(StripeCustomer).filter(
            StripeCustomer.user_id == "user_123"
        ).first()
        assert customer is not None
        assert customer.stripe_customer_id == "cus_test_123"
        assert customer.stripe_subscription_id == "sub_test_123"
        db.close()
        
        # Verify entitlements were updated
        mock_update_entitlements.assert_called_once()
    
    @patch('stripe.Webhook.construct_event')
    @patch('app.services.user_service_client.user_service_client.add_user_credits')
    @patch('stripe.checkout.Session.list_line_items')
    def test_checkout_session_completed_payment(
        self,
        mock_list_line_items,
        mock_add_credits,
        mock_construct_event
    ):
        """Test handling checkout.session.completed for one-time payment."""
        # Mock webhook event
        mock_event = {
            'type': 'checkout.session.completed',
            'id': 'evt_test_456',
            'data': {
                'object': {
                    'id': 'cs_test_456',
                    'mode': 'payment',
                    'metadata': {'user_id': 'user_456'}
                }
            }
        }
        mock_construct_event.return_value = mock_event
        
        # Mock line items
        mock_list_line_items.return_value = {
            'data': [{
                'price': {'id': 'price_credits'},
                'quantity': 5
            }]
        }
        
        # Mock user service response
        mock_add_credits.return_value = True
        
        # Send webhook
        response = client.post(
            "/stripe-webhook",
            data=json.dumps(mock_event),
            headers={
                "stripe-signature": "test_signature",
                "content-type": "application/json"
            }
        )
        
        assert response.status_code == 200
        
        # Verify credits were added (5 * 1000 = 5000 credits)
        mock_add_credits.assert_called_once_with("user_456", 5000)
    
    @patch('stripe.Webhook.construct_event')
    @patch('app.services.user_service_client.user_service_client.update_user_entitlements')
    @patch('stripe.Subscription.retrieve')
    def test_invoice_paid(
        self,
        mock_subscription_retrieve,
        mock_update_entitlements,
        mock_construct_event
    ):
        """Test handling invoice.paid event."""
        # Create customer in test DB
        db = TestingSessionLocal()
        customer = StripeCustomer(
            user_id="user_invoice",
            stripe_customer_id="cus_invoice",
            stripe_subscription_id="sub_invoice"
        )
        db.add(customer)
        db.commit()
        db.close()
        
        # Mock webhook event
        mock_event = {
            'type': 'invoice.paid',
            'id': 'evt_invoice_123',
            'data': {
                'object': {
                    'customer': 'cus_invoice',
                    'subscription': 'sub_invoice'
                }
            }
        }
        mock_construct_event.return_value = mock_event
        
        # Mock subscription
        mock_subscription_retrieve.return_value = {
            'items': {
                'data': [{'price': {'id': 'price_standard'}}]
            }
        }
        
        # Mock user service response
        mock_update_entitlements.return_value = True
        
        # Send webhook
        response = client.post(
            "/stripe-webhook",
            data=json.dumps(mock_event),
            headers={
                "stripe-signature": "test_signature",
                "content-type": "application/json"
            }
        )
        
        assert response.status_code == 200
        
        # Verify credits were reset
        mock_update_entitlements.assert_called_once_with(
            "user_invoice", 
            {"credits": 5000}
        )
    
    @patch('stripe.Webhook.construct_event')
    @patch('app.services.user_service_client.user_service_client.update_user_entitlements')
    def test_subscription_deleted(
        self,
        mock_update_entitlements,
        mock_construct_event
    ):
        """Test handling customer.subscription.deleted event."""
        # Create customer in test DB
        db = TestingSessionLocal()
        customer = StripeCustomer(
            user_id="user_cancel",
            stripe_customer_id="cus_cancel",
            stripe_subscription_id="sub_cancel"
        )
        db.add(customer)
        db.commit()
        db.close()
        
        # Mock webhook event
        mock_event = {
            'type': 'customer.subscription.deleted',
            'id': 'evt_cancel_123',
            'data': {
                'object': {
                    'customer': 'cus_cancel'
                }
            }
        }
        mock_construct_event.return_value = mock_event
        
        # Mock user service response
        mock_update_entitlements.return_value = True
        
        # Send webhook
        response = client.post(
            "/stripe-webhook",
            data=json.dumps(mock_event),
            headers={
                "stripe-signature": "test_signature",
                "content-type": "application/json"
            }
        )
        
        assert response.status_code == 200
        
        # Verify subscription was cleared
        db = TestingSessionLocal()
        customer = db.query(StripeCustomer).filter(
            StripeCustomer.user_id == "user_cancel"
        ).first()
        assert customer.stripe_subscription_id is None
        db.close()
        
        # Verify free plan entitlements were applied
        expected_entitlements = {
            "plan": "free",
            "credits": 500,
            "model_access": "base",
            "log_retention": "none",
            "support_tier": "community"
        }
        mock_update_entitlements.assert_called_once_with(
            "user_cancel",
            expected_entitlements
        )
