from typing import Any, ClassVar, Dict, Optional
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, validator


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "payment-service"
    DEBUG: bool = False
    PORT: int = 50061

    # Database settings
    DB_HOST: str
    DB_PORT: str
    DB_USER: str
    DB_PASSWORD: str
    DB_NAME: str
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"DB_USER", "DB_PASSWORD", "DB_HOST", "DB_PORT", "DB_NAME"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required database configuration: {missing}")

        return PostgresDsn.build(
            scheme="postgresql",
            username=values.get("DB_USER"),
            password=values.get("DB_PASSWORD"),
            host=values.get("DB_HOST"),
            port=int(values.get("DB_PORT", 5432)),
            path=f"{values.get('DB_NAME')}",
        )

    # Redis settings
    REDIS_HOST: str
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URI: Optional[str] = None

    @validator("REDIS_URI", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"REDIS_HOST", "REDIS_PORT", "REDIS_DB"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required Redis configuration: {missing}")

        auth_part = f":{values.get('REDIS_PASSWORD')}@" if values.get("REDIS_PASSWORD") else ""
        return f"redis://{auth_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT', 6379)}/{values.get('REDIS_DB', 0)}"

    # Proto settings
    REPO_URL: str = ""
    GIT_TOKEN: str = ""

    # GCS settings
    GCS_CRED: str = ""
    BUCKET_NAME: str = ""

    FRONTEND_URL: str = ""

    # Kafka Settings
    BOOTSTRAP_SERVERS: str = "**************:9092"
    KAFKA_TOKEN_USAGE_TOPIC: str = "token-usage-events"

    # Payment Service Specific Settings
    HTTP_PORT: int = 8000
    STRIPE_API_KEY: str
    STRIPE_WEBHOOK_SECRET: str
    STRIPE_PUBLISHABLE_KEY: Optional[str] = None
    STANDARD_PLAN_PRICE_ID: str
    PRO_PLAN_PRICE_ID: str
    TOPUP_CREDIT_PRICE_ID: str

    # Input Token to Credit Rate
    INPUT_TOKEN_TO_CREDIT_RATE: ClassVar[float] = 1.0
    OUTPUT_TOKEN_TO_CREDIT_RATE: ClassVar[float] = 1.0
    DOLLAR_TO_CREDIT_RATE: ClassVar[float] = 0.10
    TOKENS_PER_MILLION: ClassVar[int] = 1000000

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
