
# 💳 Payment Service

[![Build Status](https://img.shields.io/ci/your-org/payment-service/main.svg)](https://your-ci-link.com)
[![Coverage Status](https://img.shields.io/coveralls/github/your-org/payment-service/main.svg)](https://coveralls.io/github/your-org/payment-service)
[![Python Version](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)

A dedicated microservice for handling all subscription billing and payment logic for our platform using Stripe, with gRPC and HTTP interfaces.

---

## 🚀 Overview

This service is responsible for orchestrating user subscriptions, processing one-time payments (credit top-ups), and managing the billing lifecycle. It acts as a bridge between our platform and the Stripe payment gateway.

**Key Features:**
- **Dual Interface**: Both gRPC (for internal service communication) and HTTP (for webhooks and external APIs)
- **Stripe Integration**: Complete payment processing with subscription management
- **Event-Driven Architecture**: Webhook-based event processing with idempotency
- **Database Persistence**: PostgreSQL with comprehensive transaction logging
- **Production Ready**: Structured logging, health checks, graceful shutdown

It is designed to be completely decoupled from core user data. Its primary responsibilities are:

1. **gRPC Services**: Exposing payment operations for internal service communication
2. **HTTP Endpoints**: Handling Stripe webhooks and external payment requests
3. **Event Processing**: Translating Stripe events into user entitlement updates
4. **Transaction Management**: Comprehensive logging and audit trails

---

### 🏗️ Architectural Role

This service operates in a microservices architecture with both gRPC and HTTP communication:

```
Client/Frontend → API Gateway → Payment Service (gRPC) → Stripe API
Stripe Webhooks → Payment Service (HTTP) → User Service (gRPC)
```

- **API Gateway**: Routes authenticated requests to payment service gRPC endpoints
- **User Service**: Receives entitlement updates via gRPC calls
- **Stripe**: External payment processor with webhook integration
- **Database**: PostgreSQL for transaction logging and customer mapping

---

## 🚧 Implementation Status

### ✅ **Completed Features**

**Core Infrastructure:**
- ✅ Modern Python 3.11+ with Poetry dependency management
- ✅ Comprehensive project structure following analytics-service pattern
- ✅ PostgreSQL database with SQLAlchemy ORM and Alembic migrations
- ✅ Structured logging with configurable JSON/console output
- ✅ Environment-based configuration management

**Stripe Integration:**
- ✅ Complete Stripe service layer with error handling
- ✅ Checkout session creation (subscription & payment modes)
- ✅ Customer portal session management
- ✅ Webhook signature verification and event processing
- ✅ Transaction logging and audit trails
- ✅ Idempotency checks for webhook events

**Database Models:**
- ✅ StripeCustomer mapping (user_id ↔ stripe_customer_id)
- ✅ Transaction tracking with comprehensive metadata
- ✅ PaymentEvent logging for webhook audit trails
- ✅ Subscription plan and status management

**API Endpoints:**
- ✅ HTTP endpoints for checkout sessions and customer portal
- ✅ Stripe webhook processing with background tasks
- ✅ Health check endpoints
- ✅ Comprehensive error handling and validation

**Business Logic:**
- ✅ Plan-based entitlement management (Free, Standard, Pro)
- ✅ Credit top-up processing
- ✅ Subscription lifecycle management
- ✅ User service integration (placeholder gRPC client)

### 🔄 **In Progress / Placeholder**

**gRPC Implementation:**
- 🔄 Protocol buffer definitions created
- 🔄 gRPC code generation script ready
- 🔄 Server infrastructure prepared (placeholder implementation)
- 🔄 User service gRPC client (placeholder with logging)

**Testing:**
- 🔄 Test structure exists but needs completion
- 🔄 Mock implementations for external services

### 📋 **Next Steps**

1. **Complete gRPC Implementation:**
   - Generate Python code from proto definitions
   - Implement actual gRPC service methods
   - Replace placeholder user service client with real gRPC calls

2. **Testing & Validation:**
   - Complete unit and integration test suites
   - End-to-end testing with Stripe test environment
   - Load testing for webhook processing

3. **Production Deployment:**
   - Kubernetes manifests
   - Monitoring and alerting setup
   - Database migration scripts

---

## 🔧 Prerequisites

Ensure you have the following installed:

- [Python](https://www.python.org/) (3.9+ recommended)  
- [Poetry](https://python-poetry.org/) for dependency management  
- [Stripe CLI](https://stripe.com/docs/stripe-cli) for testing webhooks locally  
- [Docker](https://www.docker.com/) (Optional, for running other services like a database or the `user-service` locally)

You will also need a **Stripe Test Account** to get API keys.

---

## ⚙️ Setup and Installation

1. **Clone the repository:**

```bash
git clone https://github.com/your-org/payment-service.git
cd payment-service
```

2. **Install dependencies using Poetry:**

```bash
poetry install
```

3. **Configure Environment Variables:**

Copy the example environment file and fill in your details.

```bash
cp .env.example .env
```

Now open the `.env` file and populate it with your credentials from the [Stripe Dashboard](https://dashboard.stripe.com/test/apikeys).

---

## 🛠️ Configuration (Environment Variables)

| Variable                  | Description                                             | Example Value              |
|--------------------------|---------------------------------------------------------|----------------------------|
| `STRIPE_API_KEY`         | Your Stripe API secret key.                            | `sk_test_...`              |
| `STRIPE_WEBHOOK_SECRET`  | Secret to verify incoming Stripe webhooks.             | `whsec_...`                |
| `USER_SERVICE_API_URL`   | The base URL of the user-service.                      | `http://user-service:80`   |
| `STANDARD_PLAN_PRICE_ID` | The Stripe Price ID for the Standard plan.             | `price_...`                |
| `PRO_PLAN_PRICE_ID`      | The Stripe Price ID for the Pro plan.                  | `price_...`                |
| `TOPUP_CREDIT_PRICE_ID`  | The Stripe Price ID for the credit top-up product.     | `price_...`                |

---

## ▶️ Running the Service

### 1. Start the Stripe Webhook Listener

Open a new terminal and run:

```bash
stripe listen --forward-to http://localhost:8000/stripe-webhook
```

This command will output a webhook signing secret (`whsec_...`). Copy it and update `.env`.

### 2. Start the FastAPI Application

In your main terminal:

```bash
poetry run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

Your service is now running at `http://localhost:8000`.

---

## 🔑 API Endpoints

The service exposes the following main endpoints. These should be called via the API Gateway.

| Method | Endpoint                   | Description                                                                 |
|--------|----------------------------|-----------------------------------------------------------------------------|
| POST   | `/checkout-sessions`       | Creates a Stripe Checkout session for a subscription or one-time purchase. |
| POST   | `/customer-portal-sessions`| Creates a Stripe Billing Portal session for subscription management.        |
| POST   | `/stripe-webhook`          | Internal. Receives and processes Stripe events. Do not call this directly. |
| GET    | `/health`                  | Health check endpoint.                                                      |

---

## 🧪 Running Tests

Tests are written using `pytest`. They mock external dependencies (Stripe and user-service).

To run all tests:

```bash
poetry run pytest
```

To run tests with coverage:

```bash
poetry run pytest --cov=app --cov-report=html
```

---

## 🐳 Docker Deployment

Build the Docker image:

```bash
docker build -t payment-service .
```

Run the container:

```bash
docker run -p 8000:8000 --env-file .env payment-service
```

---

## 💻 Technology Stack

- **Language**: Python 3.9
- **Framework**: FastAPI
- **Server**: Uvicorn
- **Dependency Management**: Poetry
- **Payment Gateway**: Stripe
- **Inter-service Communication**: HTTP (via `httpx`)
- **Database**: SQLite (for local customer mapping)
- **Testing**: pytest, httpx-mock

---

## 📁 Project Structure

```
payment-service/
├── app/
│   ├── api/
│   │   ├── checkout.py      # Checkout and portal endpoints
│   │   ├── health.py        # Health check endpoint
│   │   └── webhooks.py      # Stripe webhook handlers
│   ├── core/
│   │   └── config.py        # Configuration settings
│   ├── models/
│   │   └── stripe_customer.py  # Database models
│   ├── services/
│   │   ├── entitlement_service.py   # Plan entitlement logic
│   │   └── user_service_client.py   # User service API client
│   └── utils/
├── tests/
│   ├── test_checkout.py
│   ├── test_webhooks.py
│   ├── test_entitlement_service.py
│   └── test_user_service_client.py
├── main.py                  # FastAPI application entry point
├── Dockerfile
├── pyproject.toml
└── README.md
```

---

## 🔄 Webhook Event Handling

The service handles the following Stripe webhook events:

### `checkout.session.completed`
- **Subscription Mode**: Creates customer mapping, provisions plan entitlements
- **Payment Mode**: Processes credit top-up purchases

### `invoice.paid`
- Resets monthly credits for subscription renewals

### `customer.subscription.deleted`
- Downgrades user to free plan entitlements

---

## 🎯 Plan Entitlements

### Standard Plan
- **Credits**: 5,000 monthly
- **Model Access**: Standard models
- **Log Retention**: 3 months
- **Support**: Email support

### Pro Plan
- **Credits**: 20,000 monthly
- **Model Access**: Premium & Custom models
- **Log Retention**: 1 year
- **Support**: 24/7 Dedicated support

### Free Plan
- **Credits**: 500 monthly
- **Model Access**: Base models only
- **Log Retention**: None
- **Support**: Community support

---
