import sys
import importlib.util

# Check if SQLAlchemy is installed
if importlib.util.find_spec("sqlalchemy") is None:
    print("Error: SQLAlchemy is not installed.")
    print("Please install it using: poetry add sqlalchemy")
    sys.exit(1)

try:
    from sqlalchemy.orm import Session
    import stripe
    from app.db.session import engine, SessionLocal
    from app.models.payment_models import Base, PaymentPlan
    from app.core.config import settings
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure all dependencies are installed: poetry install")
    sys.exit(1)

stripe.api_key = settings.STRIPE_API_KEY


def create_or_retrieve_product(product_name: str) -> stripe.Product:
    """Creates a product in Stripe or retrieves it if it already exists."""
    try:
        # Search for an active product with the given name
        products = stripe.Product.list(active=True)
        for product in products.auto_paging_iter():
            if product.name.lower() == product_name.lower():
                print(f"Product '{product_name}' already exists.")
                return product
        
        # If not found, create it
        print(f"Creating product '{product_name}' in Stripe...")
        return stripe.Product.create(name=product_name)
    except Exception as e:
        print(f"Error with Stripe product '{product_name}': {e}")
        sys.exit(1)


def create_or_retrieve_price(product: stripe.Product, amount: int, currency: str) -> stripe.Price:
    """Creates a price for a product in Stripe or retrieves it if it exists."""
    try:
        # Search for a price for the given product and amount
        prices = stripe.Price.list(product=product.id, active=True)
        for price in prices.auto_paging_iter():
            if price.unit_amount == amount and price.currency == currency:
                print(f"Price for '{product.name}' at {amount}{currency.upper()} already exists.")
                return price

        # If not found, create it
        print(f"Creating price for '{product.name}' in Stripe...")
        return stripe.Price.create(
            product=product.id,
            unit_amount=amount,
            currency=currency,
            recurring={"interval": "month"},  # Assuming monthly subscription
        )
    except Exception as e:
        print(f"Error with Stripe price for '{product.name}': {e}")
        sys.exit(1)


def init_db(db: Session):
    try:
        print("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully!")

        # Check if plans already exist
        if db.query(PaymentPlan).first():
            print("Payment plans already exist in the database.")
            return

        print("Creating/retrieving Stripe products and prices...")

        # Free Plan
        free_product = create_or_retrieve_product("Free Plan")
        free_price = create_or_retrieve_price(free_product, 0, "usd")

        # Pro Plan
        pro_product = create_or_retrieve_product("Pro Plan")
        pro_price = create_or_retrieve_price(pro_product, 1000, "usd") # $10.00 in cents

        print("Creating default payment plans in the database...")
        free_plan_db = PaymentPlan(
            plan_id_code="free",
            name="Free Plan",
            credit_amount=20,
            price=0.00,
            stripe_price_id=free_price.id,
            is_default=True,
        )
        pro_plan_db = PaymentPlan(
            plan_id_code="pro",
            name="Pro Plan",
            credit_amount=100,
            price=10.00,
            stripe_price_id=pro_price.id,
            is_default=False,
        )
        db.add(free_plan_db)
        db.add(pro_plan_db)
        db.commit()
        print("Default payment plans created.")

    except Exception as e:
        print(f"Error during DB initialization: {e}")
        db.rollback()
        sys.exit(1)


if __name__ == "__main__":
    db = SessionLocal()
    init_db(db)
    db.close()
