#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to regenerate gRPC Python code from proto definitions.
"""

import os
import subprocess
import sys
from pathlib import Path

def main():
    """Regenerate gRPC Python code from proto definitions."""
    # Get the project root directory
    project_root = Path(__file__).parent.parent
    proto_dir = project_root / "proto-definitions"
    output_dir = project_root / "app" / "grpc" / "generated"

    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create __init__.py files
    (output_dir / "__init__.py").touch()

    # Find all proto files
    proto_files = list(proto_dir.glob("*.proto"))

    if not proto_files:
        print("No proto files found in proto-definitions/")
        return 1

    print(f"Found {len(proto_files)} proto files:")
    for proto_file in proto_files:
        print(f"  - {proto_file.name}")

    # Generate Python code for each proto file
    for proto_file in proto_files:
        print(f"\nGenerating Python code for {proto_file.name}...")

        cmd = [
            sys.executable, "-m", "grpc_tools.protoc",
            f"--proto_path={proto_dir}",
            f"--python_out={output_dir}",
            f"--grpc_python_out={output_dir}",
            str(proto_file)
        ]

        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"  ✓ Generated successfully")
        except subprocess.CalledProcessError as e:
            print(f"  ✗ Error generating code: {e}")
            print(f"  stdout: {e.stdout}")
            print(f"  stderr: {e.stderr}")
            return 1

    # Fix imports in generated files
    print("\nFixing imports in generated files...")
    fix_imports(output_dir)

    print("\n✓ All proto files generated successfully!")
    return 0

def fix_imports(output_dir: Path):
    """Fix relative imports in generated gRPC files."""
    for py_file in output_dir.glob("*_pb2_grpc.py"):
        content = py_file.read_text()

        # Fix import statements to use relative imports
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('import ') and '_pb2' in line:
                # Convert absolute import to relative import
                module_name = line.split('import ')[1].strip()
                lines[i] = f'from . import {module_name}'

        py_file.write_text('\n'.join(lines))
        print(f"  ✓ Fixed imports in {py_file.name}")

if __name__ == "__main__":
    sys.exit(main())