# Application Settings
ENV=development
APP_NAME=payment-service
DEBUG=false
PORT=50051

# Database Settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=payment_service
DB_PASSWORD=paymentpass
DB_NAME=payment_db

# Redis Settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Proto
REPO_URL=
GIT_TOKEN=

# GCS Creds
GCS_CRED=
BUCKET_NAME=

FRONTEND_URL=
BOOTSTRAP_SERVERS=

# Payment Service Specific Settings
HTTP_PORT=8000
STRIPE_API_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STANDARD_PLAN_PRICE_ID=price_standard_plan_id_here
PRO_PLAN_PRICE_ID=price_pro_plan_id_here
TOPUP_CREDIT_PRICE_ID=price_credit_topup_id_here
USER_SERVICE_GRPC_URL=user-service:50052
