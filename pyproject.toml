[tool.poetry]
name = "payment-service"
version = "0.1.0"
description = "Payment Management Microservice with gRPC and Stripe Integration"
authors = ["Your Name <<EMAIL>>"]
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
# gRPC dependencies
grpcio = "^1.60.0"
grpcio-tools = "^1.60.0"
# Web framework for webhooks
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
# Payment processing
stripe = "^7.8.0"
# Database
sqlalchemy = "^2.0.25"
alembic = "^1.13.1"
psycopg2-binary = "^2.9.9"
# Configuration and validation
pydantic = "^2.5.3"
pydantic-settings = "^2.1.0"
python-dotenv = "^1.0.0"
# Logging and utilities
structlog = "^24.1.0"
# Security
python-jose = {extras = ["cryptography"], version = "^3.4.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
bcrypt = "4.0.1"
confluent-kafka = "^2.3.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.23.3"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "1.15.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py39"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pylint.messages_control]
disable = ["C0111", "C0103", "C0330", "C0326"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=term-missing"
asyncio_mode = "auto"
