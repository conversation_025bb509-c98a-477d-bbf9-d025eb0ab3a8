"""
Tests for entitlement service.
"""

import pytest
from unittest.mock import patch

from app.services.entitlement_service import EntitlementService


class TestEntitlementService:
    """Test cases for EntitlementService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.service = EntitlementService()
    
    @patch('app.core.config.settings.STANDARD_PLAN_PRICE_ID', 'price_standard')
    @patch('app.core.config.settings.PRO_PLAN_PRICE_ID', 'price_pro')
    def test_get_plan_entitlements_standard(self):
        """Test getting Standard plan entitlements."""
        entitlements = self.service.get_plan_entitlements('price_standard')
        
        expected = {
            "plan": "standard",
            "credits": 5000,
            "model_access": "standard",
            "log_retention": "3_months",
            "support_tier": "email"
        }
        
        assert entitlements == expected
    
    @patch('app.core.config.settings.STANDARD_PLAN_PRICE_ID', 'price_standard')
    @patch('app.core.config.settings.PRO_PLAN_PRICE_ID', 'price_pro')
    def test_get_plan_entitlements_pro(self):
        """Test getting Pro plan entitlements."""
        entitlements = self.service.get_plan_entitlements('price_pro')
        
        expected = {
            "plan": "pro",
            "credits": 20000,
            "model_access": "premium_custom",
            "log_retention": "1_year",
            "support_tier": "24_7_dedicated"
        }
        
        assert entitlements == expected
    
    def test_get_plan_entitlements_unknown(self):
        """Test getting entitlements for unknown price ID."""
        entitlements = self.service.get_plan_entitlements('price_unknown')
        
        expected = {
            "plan": "free",
            "credits": 500,
            "model_access": "base",
            "log_retention": "none",
            "support_tier": "community"
        }
        
        assert entitlements == expected
    
    def test_get_standard_plan_entitlements(self):
        """Test Standard plan entitlements."""
        entitlements = self.service.get_standard_plan_entitlements()
        
        assert entitlements["plan"] == "standard"
        assert entitlements["credits"] == 5000
        assert entitlements["model_access"] == "standard"
        assert entitlements["log_retention"] == "3_months"
        assert entitlements["support_tier"] == "email"
    
    def test_get_pro_plan_entitlements(self):
        """Test Pro plan entitlements."""
        entitlements = self.service.get_pro_plan_entitlements()
        
        assert entitlements["plan"] == "pro"
        assert entitlements["credits"] == 20000
        assert entitlements["model_access"] == "premium_custom"
        assert entitlements["log_retention"] == "1_year"
        assert entitlements["support_tier"] == "24_7_dedicated"
    
    def test_get_free_plan_entitlements(self):
        """Test Free plan entitlements."""
        entitlements = self.service.get_free_plan_entitlements()
        
        assert entitlements["plan"] == "free"
        assert entitlements["credits"] == 500
        assert entitlements["model_access"] == "base"
        assert entitlements["log_retention"] == "none"
        assert entitlements["support_tier"] == "community"
    
    @patch('app.core.config.settings.STANDARD_PLAN_PRICE_ID', 'price_standard')
    @patch('app.core.config.settings.PRO_PLAN_PRICE_ID', 'price_pro')
    def test_get_monthly_credits(self):
        """Test getting monthly credits for plans."""
        assert self.service.get_monthly_credits('price_standard') == 5000
        assert self.service.get_monthly_credits('price_pro') == 20000
        assert self.service.get_monthly_credits('price_unknown') is None
    
    @patch('app.core.config.settings.STANDARD_PLAN_PRICE_ID', 'price_standard')
    @patch('app.core.config.settings.PRO_PLAN_PRICE_ID', 'price_pro')
    def test_get_plan_name(self):
        """Test getting plan names."""
        assert self.service.get_plan_name('price_standard') == "standard"
        assert self.service.get_plan_name('price_pro') == "pro"
        assert self.service.get_plan_name('price_unknown') == "unknown"
