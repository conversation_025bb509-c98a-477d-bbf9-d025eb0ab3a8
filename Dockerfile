# Stage 1: Use a Python 3.11 base image to match project requirements
FROM python:3.11-slim

ARG REPO_URL
ARG GIT_TOKEN
ARG ENV


# Set environment variables for consistent behavior
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    # Set the version of Poetry to install
    POETRY_VERSION=1.8.2 \
    # Configure Poetry's home and disable virtual environments within the container
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false

# Add Poetry to the system's PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Install system dependencies
# - build-essential: Includes gcc and other tools needed to compile Python packages
# - curl: Needed to download the Poetry installer
# - git: May be required by Poetry for dependencies from Git repositories
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry using the official installer
<PERSON><PERSON> curl -sSL https://install.python-poetry.org | python3 -

# Set the working directory inside the container
WORKDIR /app

# Copy the dependency definition files
# This leverages Dock<PERSON>'s layer caching. These layers are only rebuilt if these files change.
COPY pyproject.toml poetry.lock ./

# Install project dependencies using Poetry
# --no-interaction & --no-ansi are recommended for CI/CD environments
# --no-root is used because the application code isn't copied yet
RUN poetry install --no-interaction --no-ansi --no-root

# Copy the rest of the application code into the container
COPY . .

COPY app /app/app
COPY scripts /app/scripts


# Create a non-root user for better security
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app

# Switch to the non-root user
USER app

# Expose the port the application runs on
EXPOSE 50061

RUN python -m app.scripts.generate_grpc

# Run the application
CMD ["python", "-m", "app.main"] 
