"""
Tests for checkout endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from main import app
from app.models.stripe_customer import Base, get_db, StripeCustomer

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


class TestCheckoutEndpoints:
    """Test cases for checkout endpoints."""
    
    @patch('stripe.checkout.Session.create')
    def test_create_checkout_session_new_customer(self, mock_stripe_create):
        """Test creating checkout session for new customer."""
        # Mock Stripe response
        mock_stripe_create.return_value = MagicMock(
            id="cs_test_123",
            url="https://checkout.stripe.com/pay/cs_test_123"
        )
        
        request_data = {
            "user_id": "user_123",
            "mode": "subscription",
            "price_id": "price_standard",
            "success_url": "https://example.com/success",
            "cancel_url": "https://example.com/cancel"
        }
        
        response = client.post("/checkout-sessions", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["session_id"] == "cs_test_123"
        assert data["session_url"] == "https://checkout.stripe.com/pay/cs_test_123"
        
        # Verify Stripe was called correctly
        mock_stripe_create.assert_called_once()
        call_args = mock_stripe_create.call_args[1]
        assert call_args["mode"] == "subscription"
        assert call_args["metadata"]["user_id"] == "user_123"
    
    @patch('stripe.checkout.Session.create')
    def test_create_checkout_session_existing_customer(self, mock_stripe_create):
        """Test creating checkout session for existing customer."""
        # Create existing customer in test DB
        db = TestingSessionLocal()
        existing_customer = StripeCustomer(
            user_id="user_456",
            stripe_customer_id="cus_existing",
            stripe_subscription_id="sub_existing"
        )
        db.add(existing_customer)
        db.commit()
        db.close()
        
        # Mock Stripe response
        mock_stripe_create.return_value = MagicMock(
            id="cs_test_456",
            url="https://checkout.stripe.com/pay/cs_test_456"
        )
        
        request_data = {
            "user_id": "user_456",
            "mode": "payment",
            "price_id": "price_credits",
            "success_url": "https://example.com/success",
            "cancel_url": "https://example.com/cancel",
            "quantity": 5
        }
        
        response = client.post("/checkout-sessions", json=request_data)
        
        assert response.status_code == 200
        
        # Verify existing customer was used
        call_args = mock_stripe_create.call_args[1]
        assert call_args["customer"] == "cus_existing"
    
    @patch('stripe.billing_portal.Session.create')
    def test_create_customer_portal_session(self, mock_portal_create):
        """Test creating customer portal session."""
        # Create customer in test DB
        db = TestingSessionLocal()
        customer = StripeCustomer(
            user_id="user_portal",
            stripe_customer_id="cus_portal",
            stripe_subscription_id="sub_portal"
        )
        db.add(customer)
        db.commit()
        db.close()
        
        # Mock Stripe response
        mock_portal_create.return_value = MagicMock(
            url="https://billing.stripe.com/session/portal_123"
        )
        
        request_data = {
            "user_id": "user_portal",
            "return_url": "https://example.com/account"
        }
        
        response = client.post("/customer-portal-sessions", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["portal_url"] == "https://billing.stripe.com/session/portal_123"
        
        # Verify Stripe was called correctly
        mock_portal_create.assert_called_once_with(
            customer="cus_portal",
            return_url="https://example.com/account"
        )
    
    def test_customer_portal_session_not_found(self):
        """Test customer portal session for non-existent customer."""
        request_data = {
            "user_id": "user_nonexistent",
            "return_url": "https://example.com/account"
        }
        
        response = client.post("/customer-portal-sessions", json=request_data)
        
        assert response.status_code == 404
        assert "Customer not found" in response.json()["detail"]
