# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Local development
.DS_Store
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/

# gRPC generated files
app/grpc/*_pb2.py
app/grpc/*_pb2_grpc.py

# Database
*.db
*.sqlite3
proto-definitions/
plan.md
tasks.md
memory-bank/