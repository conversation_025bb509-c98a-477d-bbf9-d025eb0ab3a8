"""
Tests for user service client.
"""

import pytest
from unittest.mock import patch, AsyncMock
import httpx

from app.services.user_service_client import UserServiceClient


class TestUserServiceClient:
    """Test cases for UserServiceClient."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.client = UserServiceClient()
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_update_user_entitlements_success(self, mock_client):
        """Test successful entitlement update."""
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_client.return_value.__aenter__.return_value.put.return_value = mock_response
        
        entitlements = {
            "plan": "standard",
            "credits": 5000,
            "model_access": "standard"
        }
        
        result = await self.client.update_user_entitlements("user_123", entitlements)
        
        assert result is True
        
        # Verify the call was made correctly
        mock_client.return_value.__aenter__.return_value.put.assert_called_once_with(
            f"{self.client.base_url}/internal/users/user_123/entitlements",
            json=entitlements,
            headers={"Content-Type": "application/json"}
        )
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_update_user_entitlements_failure(self, mock_client):
        """Test failed entitlement update."""
        # Mock failed response
        mock_response = AsyncMock()
        mock_response.status_code = 500
        mock_response.text = "Internal server error"
        mock_client.return_value.__aenter__.return_value.put.return_value = mock_response
        
        entitlements = {"credits": 5000}
        
        result = await self.client.update_user_entitlements("user_123", entitlements)
        
        assert result is False
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_update_user_entitlements_timeout(self, mock_client):
        """Test entitlement update timeout."""
        # Mock timeout exception
        mock_client.return_value.__aenter__.return_value.put.side_effect = httpx.TimeoutException("Timeout")
        
        entitlements = {"credits": 5000}
        
        result = await self.client.update_user_entitlements("user_123", entitlements)
        
        assert result is False
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_add_user_credits_success(self, mock_client):
        """Test successful credit addition."""
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        
        result = await self.client.add_user_credits("user_456", 1000)
        
        assert result is True
        
        # Verify the call was made correctly
        mock_client.return_value.__aenter__.return_value.post.assert_called_once_with(
            f"{self.client.base_url}/internal/users/user_456/credits",
            json={"credits": 1000},
            headers={"Content-Type": "application/json"}
        )
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_add_user_credits_failure(self, mock_client):
        """Test failed credit addition."""
        # Mock failed response
        mock_response = AsyncMock()
        mock_response.status_code = 404
        mock_response.text = "User not found"
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        
        result = await self.client.add_user_credits("user_nonexistent", 1000)
        
        assert result is False
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_add_user_credits_exception(self, mock_client):
        """Test credit addition with exception."""
        # Mock exception
        mock_client.return_value.__aenter__.return_value.post.side_effect = Exception("Network error")
        
        result = await self.client.add_user_credits("user_123", 1000)
        
        assert result is False
