# Action Items: Credit-Based Payment System Implementation

This document lists the specific action items required for each microservice to implement the credit-based payment system, as detailed in `plan.md`.

## 1. `user-service`

### Task 1.1: Update Organisation Data Model
*   **Action**: Add the following fields to the `Organisation` entity (or equivalent table/document):
    *   `current_plan_id`: String, nullable. Stores the ID of the currently active plan (e.g., "free", "pro").
    *   `credit_balance`: Numeric (e.g., Decimal or Integer), default 0. Stores the organisation's live credit balance.
    *   `stripe_customer_id`: String, nullable. (Confirm if already present; if so, ensure it can be linked to an organisation or org admin).
*   **Details**: Ensure appropriate indexing for `org_id` and `stripe_customer_id`.

### Task 1.2: Implement New gRPC Endpoints
*   **Action**: Create and implement the following gRPC methods:
    1.  `rpc SetInitialOrganisationCredits(SetInitialCreditsRequest { string org_id; double amount; }) returns (GenericResponse)`
        *   Logic: Sets the `credit_balance` for the specified `org_id`. Used for initial Free plan allocation.
    2.  `rpc UpdateOrganisationCreditBalance(UpdateCreditBalanceRequest { string org_id; double amount_to_change; }) returns (CreditBalanceResponse { string org_id; double new_balance; })`
        *   Logic: Atomically updates `credit_balance` by `amount_to_change` (can be positive or negative). Handles concurrency. Returns the new balance.
    3.  `rpc GetOrganisationCreditBalance(GetCreditBalanceRequest { string org_id; }) returns (CreditBalanceResponse { string org_id; double current_balance; })`
        *   Logic: Retrieves the current `credit_balance` for the `org_id`.
    4.  `rpc AssignPlanToOrganisation(AssignPlanRequest { string org_id; string plan_id; }) returns (GenericResponse)`
        *   Logic: Updates the `current_plan_id` field for the specified `org_id`.
    5.  `rpc UpdateStripeCustomerId(UpdateStripeCustomerIdRequest { string user_id_or_org_id; string stripe_customer_id; }) returns (GenericResponse)`
        *   Logic: Stores or updates the `stripe_customer_id`.
    6.  `rpc FetchStripeCustomerId(FetchStripeCustomerIdRequest { string user_id_or_org_id; }) returns (FetchStripeCustomerIdResponse { string stripe_customer_id; })`
        *   Logic: Retrieves the `stripe_customer_id`.
*   **Details**: Define corresponding protobuf messages. Ensure proper error handling and response codes.

## 2. `organisation-service`

### Task 2.1: Modify Organisation Creation Logic
*   **Action**: In the existing gRPC method for creating a new organisation (e.g., `CreateOrganisation`):
    *   After the organisation is successfully created in the database and the creating user is designated as admin:
        *   Make a gRPC call to `payment-service.ActivateDefaultPlan(org_id, default_plan_id="free")`.
*   **Details**: Handle potential errors from the `ActivateDefaultPlan` call (e.g., log and alert if critical, or implement retry).

## 3. `agent-service` (or equivalent usage-metering service)

### Task 3.1: Implement Token Calculation
*   **Action**: Develop or enhance logic to accurately count input and output tokens for every agent interaction.
*   **Details**: Consider different models and their tokenization schemes if applicable.

### Task 3.2: Implement Token-to-Credit Conversion
*   **Action**:
    *   Define a configurable parameter for the token-to-credit conversion rate (default: 1000 tokens = 1 credit).
    *   Implement logic to calculate `credits_to_deduct` based on the token count and this rate.
*   **Details**: Ensure this calculation is precise.

### Task 3.3: Implement Credit Deduction and Usage Reporting Logic
*   **Action**: At the end of each agent interaction:
    1.  Call `payment-service.DeductCredits(org_id, user_id, agent_id, credits_to_deduct, reference_details)` via gRPC.
        *   `reference_details` should include interaction ID, agent ID, etc.
    2.  If `DeductCredits` response indicates insufficient credits:
        *   Block the agent from returning its primary response to the user.
        *   Return a specific error message to the client (e.g., "Insufficient credits. Please contact your organisation admin.").
    3.  Call `analytics-service.RecordUsageEvent` (or `RecordApiRequest`) via gRPC with comprehensive details:
        *   `user_id`, `organisation_id`, `agent_id` (as `entity_id`), `entity_type="agent"`.
        *   `action="chat_interaction"`.
        *   `credits_used` (actual amount deducted, or attempted if failed).
        *   `event_metadata`: `{ "input_tokens": X, "output_tokens": Y, "deduction_status": "success" / "failed_insufficient_credits", "interaction_id": Z }`.
*   **Details**: Ensure robust error handling for calls to `payment-service` and `analytics-service`.
## 4. `payment-service`

### Task 4.1: Database Schema Setup
*   **Action**: Define and create the following tables in the `payment-service` database:
    1.  `Plans`:
        *   `plan_id` (String, PK)
        *   `name` (String)
        *   `credit_amount` (Numeric)
        *   `stripe_price_id` (String, Nullable)
        *   `is_default` (Boolean)
    2.  `CreditLedger`:
        *   `transaction_id` (UUID, PK)
        *   `organisation_id` (String, Indexed)
        *   `user_id` (String, Nullable, Indexed, for actor who initiated)
        *   `timestamp` (DateTime, Indexed)
        *   `transaction_type` (Enum/String: `INITIAL_ALLOCATION`, `USAGE_DEDUCTION`, `PLAN_PURCHASE`, `PLAN_CHANGE_ADJUSTMENT`, `MANUAL_ADJUSTMENT`)
        *   `amount` (Numeric, signed: positive for additions, negative for deductions)
        *   `balance_after_transaction` (Numeric)
        *   `reference_id` (String, Nullable, e.g., agent_interaction_id, stripe_charge_id)
        *   `notes` (String, Nullable)
*   **Details**: Seed the `Plans` table with "Free" (20 credits, default) and "Pro" (100 credits, with its Stripe Price ID) plan details.

### Task 4.2: Implement New gRPC Endpoints
*   **Action**: Create and implement the following gRPC methods:
    1.  `rpc ActivateDefaultPlan(ActivatePlanRequest { string org_id; string plan_id; }) returns (GenericResponse)`
        *   Logic:
            a.  Fetch plan details (e.g., credit amount for `plan_id`) from local `Plans` table.
            b.  Call `user-service.AssignPlanToOrganisation(org_id, plan_id)`.
            c.  Call `user-service.SetInitialOrganisationCredits(org_id, credit_amount)`.
            d.  Log transaction to `CreditLedger` (type: `INITIAL_ALLOCATION`).
    2.  `rpc DeductCredits(DeductCreditsRequest { string org_id; string user_id; string agent_id; double credits_to_deduct; map<string, string> reference_details; }) returns (DeductCreditsResponse { bool success; string error_message; })`
        *   Logic:
            a.  Call `user-service.GetOrganisationCreditBalance(org_id)`.
            b.  If `current_balance >= credits_to_deduct`:
                i.  Calculate `new_balance = current_balance - credits_to_deduct`.
                ii. Call `user-service.UpdateOrganisationCreditBalance(org_id, -credits_to_deduct)` (pass negative amount for deduction).
                iii.Log transaction to `CreditLedger` (type: `USAGE_DEDUCTION`, amount: `-credits_to_deduct`, include `reference_details`).
                iv. Return `success=true`.
            c.  Else:
                i.  Return `success=false, error_message="insufficient_credits"`.
    3.  `rpc CreateProPlanCheckoutSession(CreateCheckoutSessionRequest { string org_id; string user_id_admin; }) returns (CheckoutSessionResponse { string checkout_url; })`
        *   Logic:
            a.  Fetch Pro Plan `stripe_price_id` from `Plans` table.
            b.  Optionally, fetch/create `stripe_customer_id` for `user_id_admin` (or `org_id`) via `user-service`.
            c.  Create a Stripe Checkout session.
            d.  Return the `checkout_url`.
    4.  `rpc HandleStripeWebhook(StripeWebhookRequest { string payload; string signature; }) returns (GenericResponse)`
        *   Logic:
            a.  Verify Stripe webhook signature.
            b.  Parse payload. Handle events like `checkout.session.completed`, `invoice.payment_succeeded`.
            c.  For `checkout.session.completed` (Pro Plan purchase):
                i.  Extract `org_id`, `user_id_admin`, `stripe_customer_id` from metadata or payload.
                ii. Call `user-service.UpdateStripeCustomerId` if needed.
                iii.Call `user-service.AssignPlanToOrganisation(org_id, plan_id="pro")`.
                iv. Fetch Pro Plan credit amount.
                v.  Call `user-service.UpdateOrganisationCreditBalance(org_id, +pro_plan_credits)` (add credits for Pro plan).
                vi. Log to `CreditLedger` (type: `PLAN_PURCHASE`).
*   **Details**: Define corresponding protobuf messages. Ensure robust error handling, idempotency for webhook handling.

### Task 4.3: Implement REST API for Credit Ledger
*   **Action**: Create a REST API endpoint:
    *   `GET /organisations/{org_id}/credit-ledger`
*   **Details**: Implement pagination, filtering (e.g., by date range, transaction type). Secure appropriately.

### Task 4.4: Stripe Integration Setup
*   **Action**:
    *   Configure Stripe API keys (secret key, publishable key) and webhook signing secret securely.
    *   Define Stripe Product and Price ID for the "Pro Plan".
*   **Details**: Ensure webhook endpoint is correctly configured in Stripe dashboard.

### Task 4.5: Configuration
*   **Action**: Add configuration for:
    *   Token-to-credit conversion rate (if managed by `payment-service`).
    *   Default plan details (if not solely DB-driven).

## 5. `api-gateway`

### Task 5.1: Route New Endpoints
*   **Action**:
    *   Add routes for `payment-service` REST API endpoints (e.g., `/organisations/{org_id}/credit-ledger`).
    *   Add route for `payment-service.CreateProPlanCheckoutSession` (e.g., `POST /organisations/{org_id}/upgrade-pro`).
    *   Expose the `payment-service.HandleStripeWebhook` endpoint (e.g., `POST /webhooks/stripe`).
*   **Details**: Ensure proper authentication and authorization for these routes.

## 6. `analytics-service`

### Task 6.1: Enhance Usage Event Logging
*   **Action**: Review and ensure the `RecordUsageEvent` (or `RecordApiRequest`) gRPC method and its underlying data model can store:
    *   `organisation_id`
    *   `input_tokens` (numeric)
    *   `output_tokens` (numeric)
    *   `credits_attempted` (numeric)
    *   `credits_deducted` (numeric, can be 0 if deduction failed)
    *   `deduction_status` (String enum: "success", "failed_insufficient_credits", "failed_other_error")
*   **Details**: These might be new fields or structured within `event_metadata`. This allows detailed analysis of credit consumption patterns and deduction success/failure rates.

## 7. General / Cross-Cutting Tasks

### Task 7.1: Define Protobuf Messages
*   **Action**: Define all new and updated gRPC request/response messages in relevant `.proto` files.
*   **Details**: Regenerate gRPC client/server stubs.

### Task 7.2: Configuration Management
*   **Action**: Ensure all new configurable values (Stripe keys, conversion rates, service addresses) are managed via environment variables or a configuration service.

### Task 7.3: Testing
*   **Action**: Develop unit, integration, and end-to-end tests for all new functionalities.
*   **Details**:
    *   Mock Stripe interactions for `payment-service` tests.
    *   Test inter-service gRPC calls.
    *   Test edge cases: insufficient credits, plan activation failures, concurrent deductions.
    *   Test Stripe webhook handling with various event types and scenarios.

### Task 7.4: Documentation
*   **Action**: Update API documentation, service interaction diagrams, and operational guides.

### Task 7.5: Deployment
*   **Action**: Plan deployment strategy for each updated service.
*   **Details**: Consider database migrations for `user-service` and `payment-service`.