
# 💳 Payment Service

[![Build Status](https://img.shields.io/ci/your-org/payment-service/main.svg)](https://your-ci-link.com)  
[![Coverage Status](https://img.shields.io/coveralls/github/your-org/payment-service/main.svg)](https://coveralls.io/github/your-org/payment-service)  
[![Python Version](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)

A dedicated microservice for handling all subscription billing and payment logic for our platform using Stripe.

---

## 🚀 Overview

This service is responsible for orchestrating user subscriptions, processing one-time payments (credit top-ups), and managing the billing lifecycle. It acts as a bridge between our platform and the Stripe payment gateway.

It is designed to be completely decoupled from core user data. Its primary responsibilities are:

1. Creating secure payment sessions with Stripe.  
2. Listening for webhook events from Stripe (e.g., `payment_successful`, `subscription_cancelled`).  
3. Translating these events into specific commands and issuing them to the `user-service` to update a user's entitlements (e.g., credit balance, plan features).

---

### 🏗️ Architectural Role

This service does not work in isolation. It is a key component of our microservices architecture:

```
Client/Frontend → API Gateway → Payment Service → Stripe API  
Stripe Webhooks → Payment Service → User Service
```

- **API Gateway**: The single entry point. It handles user authentication and routes requests to this service, providing the authenticated `user_id`.
- **User Service**: The single source of truth for all user data and entitlements. This payment service tells the user service *what* to change.
- **Stripe**: The external payment processor.

---

## 🔧 Prerequisites

Ensure you have the following installed:

- [Python](https://www.python.org/) (3.9+ recommended)  
- [Poetry](https://python-poetry.org/) for dependency management  
- [Stripe CLI](https://stripe.com/docs/stripe-cli) for testing webhooks locally  
- [Docker](https://www.docker.com/) (Optional, for running other services like a database or the `user-service` locally)

You will also need a **Stripe Test Account** to get API keys.

---

## ⚙️ Setup and Installation

1. **Clone the repository:**

```bash
git clone https://github.com/your-org/payment-service.git
cd payment-service
```

2. **Install dependencies using Poetry:**

```bash
poetry install
```

3. **Configure Environment Variables:**

Copy the example environment file and fill in your details.

```bash
cp .env.example .env
```

Now open the `.env` file and populate it with your credentials from the [Stripe Dashboard](https://dashboard.stripe.com/test/apikeys).

---

## 🛠️ Configuration (Environment Variables)

| Variable                  | Description                                             | Example Value              |
|--------------------------|---------------------------------------------------------|----------------------------|
| `STRIPE_API_KEY`         | Your Stripe API secret key.                            | `sk_test_...`              |
| `STRIPE_WEBHOOK_SECRET`  | Secret to verify incoming Stripe webhooks.             | `whsec_...`                |
| `USER_SERVICE_API_URL`   | The base URL of the user-service.                      | `http://user-service:80`   |
| `STANDARD_PLAN_PRICE_ID` | The Stripe Price ID for the Standard plan.             | `price_...`                |
| `PRO_PLAN_PRICE_ID`      | The Stripe Price ID for the Pro plan.                  | `price_...`                |
| `TOPUP_CREDIT_PRICE_ID`  | The Stripe Price ID for the credit top-up product.     | `price_...`                |

---

## ▶️ Running the Service

### 1. Start the Stripe Webhook Listener

Open a new terminal and run:

```bash
stripe listen --forward-to http://localhost:8000/stripe-webhook
```

This command will output a webhook signing secret (`whsec_...`). Copy it and update `.env`.

### 2. Start the FastAPI Application

In your main terminal:

```bash
poetry run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

Your service is now running at `http://localhost:8000`.

---

## 🔑 API Endpoints

The service exposes the following main endpoints. These should be called via the API Gateway.

| Method | Endpoint                   | Description                                                                 |
|--------|----------------------------|-----------------------------------------------------------------------------|
| POST   | `/checkout-sessions`       | Creates a Stripe Checkout session for a subscription or one-time purchase. |
| POST   | `/customer-portal-sessions`| Creates a Stripe Billing Portal session for subscription management.        |
| POST   | `/stripe-webhook`          | Internal. Receives and processes Stripe events. Do not call this directly. |
| GET    | `/health`                  | Health check endpoint.                                                      |

---

## 🧪 Running Tests

Tests are written using `pytest`. They mock external dependencies (Stripe and user-service).

To run all tests:

```bash
poetry run pytest
```

---

## 💻 Technology Stack

- **Language**: Python 3.9  
- **Framework**: FastAPI  
- **Server**: Uvicorn  
- **Dependency Management**: Poetry  
- **Payment Gateway**: Stripe  
- **Inter-service Communication**: HTTP (via `httpx`)

---
